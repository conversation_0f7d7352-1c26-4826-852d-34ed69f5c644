name: Lint

on:
  push:
    branches:
      - "main"
  workflow_call:
  pull_request:
    types: [opened, reopened, synchronize]
    branches:
      - "main"

jobs:
  lint:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.10", "3.11", "3.12"]
    steps:
      - uses: actions/checkout@v4
      - name: Install uv
        uses: astral-sh/setup-uv@v6
        with:
          python-version: ${{ matrix.python-version }}
          enable-cache: true
          cache-dependency-glob: "uv.lock"
      - name: Install the project
        run: uv sync --dev
      - name: Lint
        run: uv run ruff check --output-format=github .
      - name: Typecheck
        run: uv run pyright
      - name: Format
        run: uv run ruff format
      - name: Test
        run: uv run pytest tests
  build_docs:
    needs: lint
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    steps:
      - uses: actions/checkout@v4
      - name: Configure Git Credentials
        run: |
          git config --global user.name "${{ github.actor }}"
          git config --global user.email "${{ github.actor }}@users.noreply.github.com"
      - name: Install uv
        uses: astral-sh/setup-uv@v5
        with:
          enable-cache: true
          cache-dependency-glob: "uv.lock"
      - name: Install the project
        run: uv sync --group docs
      - run: uv run util_scripts/create_results_table.py
      - run: uv run mkdocs gh-deploy --force
