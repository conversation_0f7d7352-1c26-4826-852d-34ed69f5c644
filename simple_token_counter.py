#!/usr/bin/env python3
"""
Simple token counter for AgentDojo benchmark runs.

This script provides a quick estimate of token counts without requiring
full pipeline setup or API keys.
"""

import sys
from pathlib import Path

# Add the src directory to the path so we can import agentdojo
sys.path.insert(0, str(Path(__file__).parent / "src"))

try:
    import tiktoken
except ImportError:
    print("tiktoken not installed. Install with: pip install tiktoken")
    sys.exit(1)

from agentdojo.task_suite.load_suites import get_suites
from agentdojo.agent_pipeline.agent_pipeline import load_system_message


def count_tokens_simple(text: str) -> int:
    """Count tokens using GPT-4 tokenizer."""
    try:
        tokenizer = tiktoken.encoding_for_model("gpt-4")
        return len(tokenizer.encode(text))
    except Exception:
        # Fallback: rough estimate of 4 characters per token
        return len(text) // 4


def estimate_benchmark_tokens(benchmark_version: str = "v1.2.1", suite_name: str = None, with_attacks: bool = True, detailed: bool = False):
    """Estimate total tokens for a benchmark run."""

    # Get suites
    try:
        if suite_name:
            from agentdojo.task_suite.load_suites import get_suite
            suites = {suite_name: get_suite(benchmark_version, suite_name)}
        else:
            suites = get_suites(benchmark_version)
    except Exception as e:
        print(f"Error loading suites: {e}")
        return

    # System message tokens (constant across all tasks)
    system_message = load_system_message(None)
    system_tokens = count_tokens_simple(system_message)

    print(f"Token estimation for AgentDojo benchmark {benchmark_version}")
    print("=" * 60)
    print(f"System message: {system_tokens} tokens")
    if detailed:
        print(f"System message content: {repr(system_message[:100])}...")
    print()

    total_tokens = 0
    total_tasks = 0
    component_breakdown = {
        'system_messages': 0,
        'user_prompts': 0,
        'tool_schemas': 0,
        'attack_injections': 0,
        'estimated_responses': 0,
        'estimated_tool_results': 0
    }
    
    for suite_name, suite in suites.items():
        print(f"Suite: {suite_name}")
        print(f"  User tasks: {len(suite.user_tasks)}")
        print(f"  Injection tasks: {len(suite.injection_tasks)}")
        
        # Estimate tool schema tokens (rough)
        tool_schema_tokens = len(suite.tools) * 150  # ~150 tokens per tool on average
        print(f"  Tool schemas: ~{tool_schema_tokens} tokens")
        
        suite_tokens = 0
        suite_tasks = 0
        
        # Count user task prompts
        user_task_tokens = 0
        for user_task in suite.user_tasks.values():
            user_task_tokens += count_tokens_simple(user_task.PROMPT)
        
        if with_attacks:
            # With attacks: each user task runs against each injection task
            num_combinations = len(suite.user_tasks) * len(suite.injection_tasks)

            # Each task combination includes:
            # - System message
            # - User task prompt
            # - Tool schemas
            # - Attack injection (~100-500 tokens depending on attack)
            # - Estimated LLM response (~200 tokens)
            # - Estimated tool results (~300 tokens)

            avg_user_prompt = user_task_tokens // len(suite.user_tasks) if suite.user_tasks else 0
            avg_attack_injection = 200  # Conservative estimate
            estimated_response = 200
            estimated_tool_results = 300

            tokens_per_task = (system_tokens + avg_user_prompt + tool_schema_tokens +
                             avg_attack_injection + estimated_response + estimated_tool_results)

            suite_tokens = tokens_per_task * num_combinations
            suite_tasks = num_combinations

            # Update component breakdown
            component_breakdown['system_messages'] += system_tokens * num_combinations
            component_breakdown['user_prompts'] += avg_user_prompt * num_combinations
            component_breakdown['tool_schemas'] += tool_schema_tokens * num_combinations
            component_breakdown['attack_injections'] += avg_attack_injection * num_combinations
            component_breakdown['estimated_responses'] += estimated_response * num_combinations
            component_breakdown['estimated_tool_results'] += estimated_tool_results * num_combinations

            print(f"  Attack combinations: {num_combinations}")
            print(f"  Avg tokens per combination: {tokens_per_task:,}")

            if detailed:
                print(f"    - System message: {system_tokens}")
                print(f"    - User prompt (avg): {avg_user_prompt}")
                print(f"    - Tool schemas: {tool_schema_tokens}")
                print(f"    - Attack injection: {avg_attack_injection}")
                print(f"    - Est. response: {estimated_response}")
                print(f"    - Est. tool results: {estimated_tool_results}")

        else:
            # Without attacks: just user tasks
            num_tasks = len(suite.user_tasks)

            avg_user_prompt = user_task_tokens // num_tasks if num_tasks > 0 else 0
            estimated_response = 200
            estimated_tool_results = 300

            tokens_per_task = (system_tokens + avg_user_prompt + tool_schema_tokens +
                             estimated_response + estimated_tool_results)

            suite_tokens = tokens_per_task * num_tasks
            suite_tasks = num_tasks

            # Update component breakdown
            component_breakdown['system_messages'] += system_tokens * num_tasks
            component_breakdown['user_prompts'] += avg_user_prompt * num_tasks
            component_breakdown['tool_schemas'] += tool_schema_tokens * num_tasks
            component_breakdown['estimated_responses'] += estimated_response * num_tasks
            component_breakdown['estimated_tool_results'] += estimated_tool_results * num_tasks

            print(f"  Avg tokens per task: {tokens_per_task:,}")

            if detailed:
                print(f"    - System message: {system_tokens}")
                print(f"    - User prompt (avg): {avg_user_prompt}")
                print(f"    - Tool schemas: {tool_schema_tokens}")
                print(f"    - Est. response: {estimated_response}")
                print(f"    - Est. tool results: {estimated_tool_results}")

        print(f"  Suite total: {suite_tokens:,} tokens ({suite_tasks} tasks)")
        print()
        
        total_tokens += suite_tokens
        total_tasks += suite_tasks
    
    print("=" * 60)
    print(f"TOTAL ESTIMATED TOKENS: {total_tokens:,}")
    print(f"Total tasks: {total_tasks}")
    if total_tasks > 0:
        print(f"Average tokens per task: {total_tokens // total_tasks:,}")

    if detailed and total_tokens > 0:
        print("\nToken breakdown by component:")
        for component, tokens in component_breakdown.items():
            percentage = (tokens / total_tokens) * 100
            print(f"  {component.replace('_', ' ').title()}: {tokens:,} tokens ({percentage:.1f}%)")

    # Cost estimates for common models
    print("\nEstimated costs (input tokens only):")
    models_costs = {
        "GPT-4": 0.003,  # per 1K tokens
        "GPT-4o": 0.0025,  # per 1K tokens
        "Claude-3.5-Sonnet": 0.003,  # per 1K tokens
        "Claude-3-Haiku": 0.00025,  # per 1K tokens
    }

    for model, cost_per_1k in models_costs.items():
        estimated_cost = (total_tokens / 1000) * cost_per_1k
        print(f"  {model}: ${estimated_cost:.2f}")


def main():
    import argparse

    parser = argparse.ArgumentParser(description="Simple token counter for AgentDojo")
    parser.add_argument("--benchmark-version", default="v1.2.1", help="Benchmark version")
    parser.add_argument("--suite", help="Specific suite (default: all suites)")
    parser.add_argument("--no-attacks", action="store_true", help="Count without attacks (utility only)")
    parser.add_argument("--detailed", action="store_true", help="Show detailed breakdown")

    args = parser.parse_args()

    estimate_benchmark_tokens(
        benchmark_version=args.benchmark_version,
        suite_name=args.suite,
        with_attacks=not args.no_attacks,
        detailed=args.detailed
    )


if __name__ == "__main__":
    main()
