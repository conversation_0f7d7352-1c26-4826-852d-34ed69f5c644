from typing import Annotated

from pydantic import BaseModel

from agentdojo.functions_runtime import Depends


class Counter(BaseModel):
    counter: int
    counter_description: str

    def increment(self, n: int = 1) -> int:
        self.counter += n
        return self.counter


def increment(counter: Annotated[Counter, Depends("counter")]) -> int:
    """Increases the counter by 1."""
    return counter.increment()


def increment_by(counter: Annotated[Counter, Depends("counter")], n: int) -> int:
    """Increases the counter by `n`.

    :param n: The amount to increase the counter by.
    """
    return counter.increment(n)


def get_value(counter: Annotated[Counter, Depends("counter")]) -> int:
    """Returns the value of the counter."""
    return counter.counter


def get_description(counter: Annotated[Counter, Depends("counter")]) -> str:
    """Returns the description of the counter."""
    return counter.counter_description
