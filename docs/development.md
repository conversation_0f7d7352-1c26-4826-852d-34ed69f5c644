---
hide:
  - navigation
---

# Development set-up

We use `uv`. You can follow the instructions on the [website](https://docs.astral.sh/uv) to set it up.

## Install the dev packages, lint, test, typecheck

- Install all the packages with `uv sync --dev --group docs --all-extras --no-lock` (include `--all-extras` if you want to work on the PI detector and `--group docs` if you want to run the docs offline.)
- <PERSON>t with `uv run ruff --fix`
- Format with `uv run ruff format`
- Test with `uv run pytest tests`
- Run the static type checker with `uv run pyright`

## Running the docs

You can run the docs locally with `mkdocs serve`. The docs will be available at `http://localhost:8000`.
