---
hide:
  - navigation
---

# AgentDojo: A Dynamic Environment to Evaluate Prompt Injection Attacks and Defenses for LLM Agents

[<PERSON><PERSON><PERSON>](https://edoardo.science)<sup>1</sup>, [<PERSON><PERSON>](https://zj-jayzhang.github.io)<sup>1</sup>, [<PERSON><PERSON>](https://www.sri.inf.ethz.ch/people/mislav)<sup>1,2</sup>, <br> [<PERSON>](https://www.sri.inf.ethz.ch/people/luca)<sup>1,2</sup>, [<PERSON>](https://marc<PERSON>scher.at)<sup>1,2</sup>, [<PERSON><PERSON><PERSON>](https://floriantramer.com)<sup>1</sup>

<sup>1</sup>ETH Zurich and <sup>2</sup>Invariant Labs

[Paper](https://arxiv.org/abs/2406.13352) | [Results](./results).

![GitHub License](https://img.shields.io/github/license/ethz-spylab/agentdojo) ![GitHub Actions Workflow Status](https://img.shields.io/github/actions/workflow/status/ethz-spylab/agentdojo/lint-docs.yaml)
 ![PyPI - Python Version](https://img.shields.io/pypi/pyversions/agentdojo) ![PyPI - Downloads](https://img.shields.io/pypi/dm/agentdojo) ![PyPI - Version](https://img.shields.io/pypi/v/agentdojo)

## Quickstart

```bash
pip install agentdojo
```

!!! warning
    Note that the API of the package is still under development and might change in the future.

If you want to use the PI detector, you need to install the `transformers` extra:

```bash
pip install "agentdojo[transformers]"
```

## Impact

- AgentDojo won a [SafeBench](https://www.mlsafety.org/safebench/winners) first prize, along with Cybench and BackdoorLLM.
- AgentDojo has been used by US and UK AISI to show the vulnerability of Claude 3.5 Sonnet (new) to prompt injections. Blogpost [here](https://www.nist.gov/news-events/news/2025/01/technical-blog-strengthening-ai-agent-hijacking-evaluations).

## FAQ

### How do I run the benchmark?

To run the benchmark, you first need to install the package. Once this is done, the benchmark can be run with the [benchmark](https://github.com/ethz-spylab/agentdojo/blob/main/src/agentdojo/scripts/benchmark.py) script. Documentation on how to use the script can be obtained with the `--help` flag.

For example, to run the `workspace` suite on the tasks 0 and 1, with `gpt-4o-2024-05-13` as the LLM, the tool filter as a defense, and the attack with tool knowlege, run the following command:

```bash
python -m agentdojo.scripts.benchmark -s workspace -ut user_task_0 \
    -ut user_task_1 --model gpt-4o-2024-05-13 \
    --defense tool_filter --attack tool_knowledge
```

To run the above, but on all suites and tasks, run the following:

```bash
python -m agentdojo.scripts.benchmark --model gpt-4o-2024-05-13 \
    --defense tool_filter --attack tool_knowledge
```

### How do I create a new defense or model?

Check out the [documentation](concepts/agent_pipeline.md) on how to create your own pipeline.

### How do I create a new attack?

Check out the [documentation](concepts/attacks.md) on attacks.

### How can I use the Dojo to create my own benchmark?

Take a look at our in-depth [documentation](concepts/task_suite_and_tasks.md) on how to create a new task suite.

## Development set-up

Take a look at the [development set-up](development.md) docs.

## Citing

If you use AgentDojo in your research, please consider citing our paper:

```bibtex
@inproceedings{
   debenedetti2024agentdojo,
   title={AgentDojo: A Dynamic Environment to Evaluate Prompt Injection Attacks and Defenses for {LLM} Agents},
   author={Edoardo Debenedetti and Jie Zhang and Mislav Balunovic and Luca Beurer-Kellner and Marc Fischer and Florian Tram{\`e}r},
   booktitle={The Thirty-eight Conference on Neural Information Processing Systems Datasets and Benchmarks Track},
   year={2024},
   url={https://openreview.net/forum?id=m1YYAQjO3w}
}
```
